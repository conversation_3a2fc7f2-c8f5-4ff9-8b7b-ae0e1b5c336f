-- Оновлення латинських назв тегів на українські
-- SQL запит для заміни Lorem Ipsum тексту на реальні українські теги фільмів

-- Оновлення записів з українськими тегами
UPDATE "public"."tags" 
SET 
    "name" = 'драма',
    "slug" = 'drama',
    "description" = 'Драматичні фільми, які розкривають глибокі людські емоції та складні життєві ситуації.',
    "meta_title" = 'Драма - жанр фільмів',
    "meta_description" = 'Найкращі драматичні фільми, які торкаються серця та змушують замислитися про життя.',
    "is_genre" = true
WHERE "id" = '01jxx55mad7b3jj6tcv86d9c40';

UPDATE "public"."tags" 
SET 
    "name" = 'комедія',
    "slug" = 'komediya',
    "description" = 'Веселі та розважальні фільми, які піднімають настрій та дарують позитивні емоції.',
    "meta_title" = 'Комедія - жанр фільмів',
    "meta_description" = 'Найсмішніші комедійні фільми для гарного настрою та відпочинку.',
    "is_genre" = true
WHERE "id" = '01jxx55manemh437j3cy6w2xyr';

UPDATE "public"."tags" 
SET 
    "name" = 'бойовик',
    "slug" = 'boyovyk',
    "description" = 'Динамічні фільми з захоплюючими сценами дії, погонями та боротьбою.',
    "meta_title" = 'Бойовик - жанр фільмів',
    "meta_description" = 'Найкращі бойовики з неймовірними сценами дії та адреналіном.',
    "is_genre" = true
WHERE "id" = '01jxx55mavb5wk9hbcjgzrqtah';

UPDATE "public"."tags" 
SET 
    "name" = 'трилер',
    "slug" = 'tryler',
    "description" = 'Напружені фільми, які тримають глядача в постійному очікуванні та саспенсі.',
    "meta_title" = 'Трилер - жанр фільмів',
    "meta_description" = 'Захоплюючі трилери, які не дають розслабитися до самого кінця.',
    "is_genre" = true
WHERE "id" = '01jxx55mb0es38wkr4mar46n3p';

UPDATE "public"."tags" 
SET 
    "name" = 'фантастика',
    "slug" = 'fantastyka',
    "description" = 'Науково-фантастичні фільми про майбутнє, космос та неймовірні технології.',
    "meta_title" = 'Фантастика - жанр фільмів',
    "meta_description" = 'Найкращі науково-фантастичні фільми про майбутнє та космічні пригоди.',
    "is_genre" = true
WHERE "id" = '01jxx55mb50nt4v12d5vqnj514';

UPDATE "public"."tags" 
SET 
    "name" = 'жахи',
    "slug" = 'zhahy',
    "description" = 'Страшні фільми, які викликають жах, напругу та адреналін.',
    "meta_title" = 'Жахи - жанр фільмів',
    "meta_description" = 'Найстрашніші фільми жахів для любителів гострих відчуттів.',
    "is_genre" = true
WHERE "id" = '01jxx55mbazjg9q874xx1ahh97';

UPDATE "public"."tags" 
SET 
    "name" = 'романтика',
    "slug" = 'romantyka',
    "description" = 'Романтичні фільми про кохання, стосунки та емоційні переживання.',
    "meta_title" = 'Романтика - жанр фільмів',
    "meta_description" = 'Найкращі романтичні фільми про кохання та стосунки.',
    "is_genre" = true
WHERE "id" = '01jxx55mbgjbcrtj9jvcgsy9je';

UPDATE "public"."tags" 
SET 
    "name" = 'пригоди',
    "slug" = 'prygody',
    "description" = 'Пригодницькі фільми з захоплюючими подорожами та неймовірними відкриттями.',
    "meta_title" = 'Пригоди - жанр фільмів',
    "meta_description" = 'Найкращі пригодницькі фільми для всієї родини.',
    "is_genre" = true
WHERE "id" = '01jxx55mbng69tx5z5h2hc7k6c';

UPDATE "public"."tags" 
SET 
    "name" = 'кримінал',
    "slug" = 'kryminal',
    "description" = 'Кримінальні фільми про злочинців, детективів та боротьбу зі злочинністю.',
    "meta_title" = 'Кримінал - жанр фільмів',
    "meta_description" = 'Найкращі кримінальні фільми та детективи.',
    "is_genre" = true
WHERE "id" = '01jxx55mbts5sh0t1y9me8n3aw';

UPDATE "public"."tags" 
SET 
    "name" = 'мюзикл',
    "slug" = 'myuzikl',
    "description" = 'Музичні фільми з піснями, танцями та яскравими номерами.',
    "meta_title" = 'Мюзикл - жанр фільмів',
    "meta_description" = 'Найкращі мюзикли з незабутньою музикою та хореографією.',
    "is_genre" = true
WHERE "id" = '01jxx55mbz3vy6fwhrvppepg8a';

UPDATE "public"."tags" 
SET 
    "name" = 'вестерн',
    "slug" = 'vestern',
    "description" = 'Фільми про Дикий Захід, ковбоїв та життя на американському фронтирі.',
    "meta_title" = 'Вестерн - жанр фільмів',
    "meta_description" = 'Класичні вестерни про ковбоїв та Дикий Захід.',
    "is_genre" = true
WHERE "id" = '01jxx55mc418w9vx9z0h46rm9v';

UPDATE "public"."tags" 
SET 
    "name" = 'фентезі',
    "slug" = 'fentezi',
    "description" = 'Фантастичні фільми з магією, чарівниками та казковими світами.',
    "meta_title" = 'Фентезі - жанр фільмів',
    "meta_description" = 'Найкращі фентезійні фільми з магією та чарівними пригодами.',
    "is_genre" = true
WHERE "id" = '01jxx55mc9rc9k3ad1rqdfsdmb';

UPDATE "public"."tags" 
SET 
    "name" = 'анімація',
    "slug" = 'animatsiya',
    "description" = 'Анімаційні фільми для дітей та дорослих з яскравою графікою.',
    "meta_title" = 'Анімація - жанр фільмів',
    "meta_description" = 'Найкращі анімаційні фільми для всієї родини.',
    "is_genre" = true
WHERE "id" = '01jxx55mcejxh6fqr9cv8jmg4b';

UPDATE "public"."tags" 
SET 
    "name" = 'документальний',
    "slug" = 'dokumentalnyy',
    "description" = 'Документальні фільми про реальні події, людей та явища.',
    "meta_title" = 'Документальний - жанр фільмів',
    "meta_description" = 'Найкращі документальні фільми про реальне життя.',
    "is_genre" = true
WHERE "id" = '01jxx55mck50sje3zt3cj3eq5r';

UPDATE "public"."tags" 
SET 
    "name" = 'біографічний',
    "slug" = 'biografichnyy',
    "description" = 'Фільми про життя реальних видатних особистостей та їхні досягнення.',
    "meta_title" = 'Біографічний - жанр фільмів',
    "meta_description" = 'Найкращі біографічні фільми про видатних людей.',
    "is_genre" = true
WHERE "id" = '01jxx55mf22w3nd5ac3mp6azww';

UPDATE "public"."tags" 
SET 
    "name" = 'історичний',
    "slug" = 'istorychnyy',
    "description" = 'Фільми про історичні події, епохи та видатних історичних діячів.',
    "meta_title" = 'Історичний - жанр фільмів',
    "meta_description" = 'Найкращі історичні фільми про минулі епохи.',
    "is_genre" = true
WHERE "id" = '01jxx55mcrh35fn258s5w2t976';

UPDATE "public"."tags" 
SET 
    "name" = 'спорт',
    "slug" = 'sport',
    "description" = 'Фільми про спорт, спортсменів та спортивні досягнення.',
    "meta_title" = 'Спорт - тематика фільмів',
    "meta_description" = 'Найкращі спортивні фільми про перемоги та поразки.',
    "is_genre" = false
WHERE "id" = '01jxx55mcx9pkv0xj8tkskj1sq';

UPDATE "public"."tags" 
SET 
    "name" = 'війна',
    "slug" = 'viyna',
    "description" = 'Воєнні фільми про битви, героїзм та жертви під час війн.',
    "meta_title" = 'Війна - тематика фільмів',
    "meta_description" = 'Найкращі воєнні фільми про мужність та героїзм.',
    "is_genre" = false
WHERE "id" = '01jxx55md2sjn6parsexbxbdcr';

UPDATE "public"."tags" 
SET 
    "name" = 'сім\'я',
    "slug" = 'simya',
    "description" = 'Сімейні фільми про родинні цінності та стосунки.',
    "meta_title" = 'Сім\'я - тематика фільмів',
    "meta_description" = 'Найкращі сімейні фільми для перегляду всією родиною.',
    "is_genre" = false
WHERE "id" = '01jxx55md7r4tx8ge8sstfw952';

UPDATE "public"."tags" 
SET 
    "name" = 'дружба',
    "slug" = 'druzhba',
    "description" = 'Фільми про справжню дружбу, вірність та взаємодопомогу.',
    "meta_title" = 'Дружба - тематика фільмів',
    "meta_description" = 'Найкращі фільми про дружбу та вірність.',
    "is_genre" = false
WHERE "id" = '01jxx55mddbmep8jggk9khsbmm';

UPDATE "public"."tags" 
SET 
    "name" = 'помста',
    "slug" = 'pomsta',
    "description" = 'Фільми про помсту, справедливість та відплату за кривди.',
    "meta_title" = 'Помста - тематика фільмів',
    "meta_description" = 'Найкращі фільми про помсту та справедливість.',
    "is_genre" = false
WHERE "id" = '01jxx55mdj204gsn8dkx49xkwv';

UPDATE "public"."tags" 
SET 
    "name" = 'кохання',
    "slug" = 'kokhannya',
    "description" = 'Фільми про кохання, романтичні стосунки та емоційні переживання.',
    "meta_title" = 'Кохання - тематика фільмів',
    "meta_description" = 'Найкращі фільми про кохання та романтичні стосунки.',
    "is_genre" = false
WHERE "id" = '01jxx55mdqtfzjwypmdtgpfxyj';

UPDATE "public"."tags" 
SET 
    "name" = 'виживання',
    "slug" = 'vyzhyvannya',
    "description" = 'Фільми про виживання в екстремальних умовах та боротьбу за життя.',
    "meta_title" = 'Виживання - тематика фільмів',
    "meta_description" = 'Найкращі фільми про виживання та боротьбу за життя.',
    "is_genre" = false
WHERE "id" = '01jxx55mdwf69hnwtfqzs74agr';

UPDATE "public"."tags" 
SET 
    "name" = 'подорожі',
    "slug" = 'podorozhi',
    "description" = 'Фільми про подорожі, відкриття нових місць та пригоди.',
    "meta_title" = 'Подорожі - тематика фільмів',
    "meta_description" = 'Найкращі фільми про подорожі та відкриття світу.',
    "is_genre" = false
WHERE "id" = '01jxx55me0ek8akq109rzx3tca';

UPDATE "public"."tags" 
SET 
    "name" = 'школа',
    "slug" = 'shkola',
    "description" = 'Фільми про шкільне життя, учнів та освітні пригоди.',
    "meta_title" = 'Школа - тематика фільмів',
    "meta_description" = 'Найкращі фільми про шкільне життя та освіту.',
    "is_genre" = false
WHERE "id" = '01jxx55me6672aqkz59nhpt1k1';

UPDATE "public"."tags" 
SET 
    "name" = 'робота',
    "slug" = 'robota',
    "description" = 'Фільми про професійне життя, кар\'єру та робочі стосунки.',
    "meta_title" = 'Робота - тематика фільмів',
    "meta_description" = 'Найкращі фільми про професійне життя та кар\'єру.',
    "is_genre" = false
WHERE "id" = '01jxx55mea2f6mbbefty8krkkw';

UPDATE "public"."tags" 
SET 
    "name" = 'мистецтво',
    "slug" = 'mystetstvo',
    "description" = 'Фільми про мистецтво, художників та творчий процес.',
    "meta_title" = 'Мистецтво - тематика фільмів',
    "meta_description" = 'Найкращі фільми про мистецтво та творчість.',
    "is_genre" = false
WHERE "id" = '01jxx55mef2pjesrkf8kwzef44';

UPDATE "public"."tags" 
SET 
    "name" = 'музика',
    "slug" = 'muzyka',
    "description" = 'Фільми про музику, музикантів та музичну індустрію.',
    "meta_title" = 'Музика - тематика фільмів',
    "meta_description" = 'Найкращі фільми про музику та музикантів.',
    "is_genre" = false
WHERE "id" = '01jxx55mem13kzszehzn0je1rc';

UPDATE "public"."tags" 
SET 
    "name" = 'природа',
    "slug" = 'pryroda',
    "description" = 'Фільми про природу, тварин та екологічні проблеми.',
    "meta_title" = 'Природа - тематика фільмів',
    "meta_description" = 'Найкращі фільми про природу та навколишнє середовище.',
    "is_genre" = false
WHERE "id" = '01jxx55mesr4sj1qaqdzjvf0k5';

-- Додаткові теги
UPDATE "public"."tags"
SET
    "name" = 'технології',
    "slug" = 'tekhnologiyi',
    "description" = 'Фільми про сучасні технології, штучний інтелект та цифровий світ.',
    "meta_title" = 'Технології - тематика фільмів',
    "meta_description" = 'Найкращі фільми про технології та цифрове майбутнє.',
    "is_genre" = false
WHERE "id" = '01jxx55mey88k64sc0w2fza54r';

UPDATE "public"."tags"
SET
    "name" = 'космос',
    "slug" = 'kosmos',
    "description" = 'Фільми про космічні подорожі, дослідження всесвіту та астрономію.',
    "meta_title" = 'Космос - тематика фільмів',
    "meta_description" = 'Найкращі фільми про космос та міжзоряні подорожі.',
    "is_genre" = false
WHERE "id" = '01jxx55mf7cbxrwe8rvsq7xm9z';

UPDATE "public"."tags"
SET
    "name" = 'медицина',
    "slug" = 'medytsyna',
    "description" = 'Фільми про лікарів, медицину та боротьбу за життя пацієнтів.',
    "meta_title" = 'Медицина - тематика фільмів',
    "meta_description" = 'Найкращі медичні фільми про лікарів та медицину.',
    "is_genre" = false
WHERE "id" = '01jxx55mfcbcwz7yw44k2qr21z';

UPDATE "public"."tags"
SET
    "name" = 'політика',
    "slug" = 'polityka',
    "description" = 'Фільми про політику, владу та суспільні процеси.',
    "meta_title" = 'Політика - тематика фільмів',
    "meta_description" = 'Найкращі політичні фільми та трилери.',
    "is_genre" = false
WHERE "id" = '01jxx55mfh4xypkf7kqrvjrc3e';

UPDATE "public"."tags"
SET
    "name" = 'релігія',
    "slug" = 'religiya',
    "description" = 'Фільми про релігію, віру та духовні пошуки.',
    "meta_title" = 'Релігія - тематика фільмів',
    "meta_description" = 'Найкращі фільми про релігію та духовність.',
    "is_genre" = false
WHERE "id" = '01jxx55mfpt6p4mk7jw7n0x9wh';

UPDATE "public"."tags"
SET
    "name" = 'психологія',
    "slug" = 'psykhologiya',
    "description" = 'Фільми про психологію, ментальне здоров\'я та внутрішній світ людини.',
    "meta_title" = 'Психологія - тематика фільмів',
    "meta_description" = 'Найкращі психологічні фільми та драми.',
    "is_genre" = false
WHERE "id" = '01jxx55mftctzw1fp1kw3p7xgp';

UPDATE "public"."tags"
SET
    "name" = 'соціальні проблеми',
    "slug" = 'sotsialni-problemy',
    "description" = 'Фільми про соціальні проблеми, нерівність та суспільні виклики.',
    "meta_title" = 'Соціальні проблеми - тематика фільмів',
    "meta_description" = 'Фільми, які розкривають важливі соціальні питання.',
    "is_genre" = false
WHERE "id" = '01jxx55mfz3b2tv6m1mhrzf5ee';

UPDATE "public"."tags"
SET
    "name" = 'екологія',
    "slug" = 'ekologiya',
    "description" = 'Фільми про екологічні проблеми, захист довкілля та кліматичні зміни.',
    "meta_title" = 'Екологія - тематика фільмів',
    "meta_description" = 'Найкращі фільми про екологію та захист природи.',
    "is_genre" = false
WHERE "id" = '01jxx55mga8caepya068wztghf';

UPDATE "public"."tags"
SET
    "name" = 'економіка',
    "slug" = 'ekonomika',
    "description" = 'Фільми про економіку, бізнес та фінансові кризи.',
    "meta_title" = 'Економіка - тематика фільмів',
    "meta_description" = 'Найкращі фільми про економіку та бізнес.',
    "is_genre" = false
WHERE "id" = '01jxx55mggajxcxj72x65v0fbb';

UPDATE "public"."tags"
SET
    "name" = 'наука',
    "slug" = 'nauka',
    "description" = 'Фільми про наукові відкриття, дослідження та вчених.',
    "meta_title" = 'Наука - тематика фільмів',
    "meta_description" = 'Найкращі наукові фільми та документальні стрічки.',
    "is_genre" = false
WHERE "id" = '01jxx55mgpqhbwpcagtad78x4s';

UPDATE "public"."tags"
SET
    "name" = 'детектив',
    "slug" = 'detektyv',
    "description" = 'Детективні фільми з розслідуваннями, таємницями та загадками.',
    "meta_title" = 'Детектив - жанр фільмів',
    "meta_description" = 'Найкращі детективні фільми з захоплюючими розслідуваннями.',
    "is_genre" = true
WHERE "id" = '01jxx55mgwj0x27wetw31fsjyz';

UPDATE "public"."tags"
SET
    "name" = 'містика',
    "slug" = 'mistyka',
    "description" = 'Містичні фільми з надприродними явищами та таємничими подіями.',
    "meta_title" = 'Містика - жанр фільмів',
    "meta_description" = 'Найкращі містичні фільми з надприродними елементами.',
    "is_genre" = true
WHERE "id" = '01jxx55mh2nr1c01vewd55wxzq';

UPDATE "public"."tags"
SET
    "name" = 'супергерої',
    "slug" = 'supergeroyі',
    "description" = 'Фільми про супергероїв, їхні суперсили та боротьбу зі злом.',
    "meta_title" = 'Супергерої - тематика фільмів',
    "meta_description" = 'Найкращі супергеройські фільми Marvel та DC.',
    "is_genre" = false
WHERE "id" = '01jxx55mh89cpsxab27whvjc6n';

UPDATE "public"."tags"
SET
    "name" = 'апокаліпсис',
    "slug" = 'apokalipsys',
    "description" = 'Фільми про кінець світу, апокаліпсис та виживання після катастроф.',
    "meta_title" = 'Апокаліпсис - тематика фільмів',
    "meta_description" = 'Найкращі постапокаліптичні фільми про виживання.',
    "is_genre" = false
WHERE "id" = '01jxx55mhd2zqzek33z9nt2bpx';

UPDATE "public"."tags"
SET
    "name" = 'зомбі',
    "slug" = 'zombi',
    "description" = 'Фільми про зомбі, епідемії та боротьбу за виживання.',
    "meta_title" = 'Зомбі - тематика фільмів',
    "meta_description" = 'Найкращі фільми про зомбі та зомбі-апокаліпсис.',
    "is_genre" = false
WHERE "id" = '01jxx55mhjn8xxza2yegzt37t3';

UPDATE "public"."tags"
SET
    "name" = 'вампіри',
    "slug" = 'vampiry',
    "description" = 'Фільми про вампірів, їхнє життя та боротьбу з мисливцями.',
    "meta_title" = 'Вампіри - тематика фільмів',
    "meta_description" = 'Найкращі фільми про вампірів та надприродні істоти.',
    "is_genre" = false
WHERE "id" = '01jxx55mhq8saaj042qtmc7vbb';
